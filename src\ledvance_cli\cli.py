"""
Ledvance CLI 应用程序的主入口点。
"""

import logging
import os
import pkgutil
import sys
from pathlib import Path

import click

from ledvance_cli import commands
from ledvance_cli.config import initialize_config
from ledvance_cli.core.exceptions import CLIError
from ledvance_cli.logger import setup_logging, LogLevel

# 上下文对象，用于将全局选项传递给子命令
class AppContext:
    def __init__(self):
        self.verbose = False
        self.dry_run = False
        self.output_format = "text"

@click.group()
@click.option(
    "--log-level",
    type=click.Choice(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], case_sensitive=False),
    default="INFO",
    help="设置日志记录级别。",
)
@click.option(
    "--output",
    "output_format",
    type=click.Choice(["text", "json", "yaml"], case_sensitive=False),
    default="text",
    help="设置输出格式。",
)
@click.option("--dry-run", is_flag=True, help="执行空运行，不进行实际更改。")
@click.option("-v", "--verbose", is_flag=True, help="启用详细输出。")
@click.version_option(package_name="ledvance_cli")
@click.pass_context
def main(
    ctx: click.Context,
    log_level: LogLevel,
    output_format: str,
    dry_run: bool,
    verbose: bool,
) -> None:
    """
    Ledvance CLI - 一个现代化且可扩展的Tuya和Ezviz平台管理工具。
    """
    # 尽早设置日志记录
    setup_logging(log_level)

    # 初始化上下文对象
    ctx.obj = AppContext()
    ctx.obj.verbose = verbose
    ctx.obj.dry_run = dry_run
    ctx.obj.output_format = output_format

    try:
        # 初始化多层级配置系统
        initialize_config()
    except CLIError as e:
        logging.error(str(e), exc_info=verbose)
        sys.exit(e.exit_code)

# --- 命令发现 ---
def _discover_commands():
    """从 'commands' 模块动态发现并加载命令。"""
    command_path = os.path.dirname(commands.__file__)
    for _, name, _ in pkgutil.iter_modules([command_path]):
        module_name = f"ledvance_cli.commands.{name}"
        __import__(module_name)
        module = sys.modules[module_name]
        for attr in dir(module):
            obj = getattr(module, attr)
            if isinstance(obj, click.Command):
                main.add_command(obj)

_discover_commands()

# --- Shell 自动补全支持 ---
@main.command()
@click.argument('shell', type=click.Choice(['bash', 'zsh', 'fish', 'powershell']))
@click.option('--install', is_flag=True, help='自动安装补全脚本到shell配置文件')
def completion(shell, install):
    """
    生成并安装shell自动补全脚本。

    支持的shell: bash, zsh, fish, powershell

    示例:
    \b
    # 生成bash补全脚本
    ledvance completion bash

    # 自动安装到bash配置文件
    ledvance completion bash --install

    # 生成zsh补全脚本
    ledvance completion zsh
    """
    import subprocess
    import platform
    from pathlib import Path

    prog_name = 'ledvance'
    complete_var = f'_{prog_name.upper()}_COMPLETE'

    if shell in ['bash', 'zsh', 'fish']:
        # 使用Click内置的补全功能
        try:
            env = os.environ.copy()
            env[complete_var] = f'{shell}_source'

            result = subprocess.run(
                [prog_name],
                env=env,
                capture_output=True,
                text=True,
                check=True
            )
            completion_script = result.stdout

        except subprocess.CalledProcessError as e:
            click.echo(f"生成{shell}补全脚本失败: {e}", err=True)
            return
        except Exception as e:
            click.echo(f"错误: {e}", err=True)
            return

    elif shell == 'powershell':
        # PowerShell补全脚本
        completion_script = _generate_powershell_completion(prog_name)

    if install:
        _install_completion_script(shell, completion_script, prog_name)
    else:
        click.echo(completion_script)

def _generate_powershell_completion(prog_name):
    """生成PowerShell补全脚本"""
    return f'''# PowerShell completion for {prog_name}
Register-ArgumentCompleter -Native -CommandName {prog_name} -ScriptBlock {{
    param($wordToComplete, $commandAst, $cursorPosition)

    $commands = @(
        'ezviz-global-panel-create',
        'ezviz-global-panel-upgrade',
        'ezviz-panel-gray',
        'ezviz-panel-set',
        'ezviz-panel-check',
        'ezviz-panel-upgrade',
        'ezviz-panel-create',
        'tuya-dp-check',
        'tuya-login',
        'tuya-sync-dp',
        'tuya-sync-product',
        'completion'
    )

    $options = @(
        '--log-level',
        '--output',
        '--dry-run',
        '--verbose',
        '--version',
        '--help'
    )

    # 获取当前命令行
    $line = $commandAst.CommandElements

    if ($line.Count -eq 1) {{
        # 只输入了程序名，补全命令
        $commands | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
            [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterValue', $_)
        }}

        # 补全全局选项
        $options | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
            [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterName', $_)
        }}
    }} elseif ($line.Count -eq 2) {{
        # 输入了命令，补全该命令的选项
        $command = $line[1].Value

        switch ($command) {{
            'ezviz-panel-check' {{
                @('--env', '--category', '--help') | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
                    [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterName', $_)
                }}
            }}
            'ezviz-global-panel-upgrade' {{
                @('--env', '--type', '--dir', '--version', '--app-version', '--users', '--modules', '--help') | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
                    [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterName', $_)
                }}
            }}
            'ezviz-panel-upgrade' {{
                @('--env', '--category', '--version', '--help') | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
                    [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterName', $_)
                }}
            }}
            'tuya-dp-check' {{
                @('--env', '--category', '--dp-code', '--help') | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
                    [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterName', $_)
                }}
            }}
            'tuya-login' {{
                @('--env', '--help') | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
                    [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterName', $_)
                }}
            }}
            'tuya-sync-dp' {{
                @('--env', '--category', '--pid', '--help') | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
                    [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterName', $_)
                }}
            }}
            'tuya-sync-product' {{
                @('--env', '--main-category', '--category', '--rn-name', '--remark', '--help') | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
                    [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterName', $_)
                }}
            }}
            'completion' {{
                @('bash', 'zsh', 'fish', 'powershell', '--install', '--help') | Where-Object {{ $_ -like "$wordToComplete*" }} | ForEach-Object {{
                    [System.Management.Automation.CompletionResult]::new($_, $_, 'ParameterValue', $_)
                }}
            }}
        }}
    }}
}}

# 添加别名支持
if (Get-Command Set-Alias -ErrorAction SilentlyContinue) {{
    Set-Alias -Name lv -Value {prog_name}
    Register-ArgumentCompleter -Native -CommandName lv -ScriptBlock $function:TabExpansion2
}}
'''

def _install_completion_script(shell, script, prog_name):
    """安装补全脚本到相应的shell配置文件"""
    import platform
    from pathlib import Path

    home = Path.home()
    system = platform.system().lower()

    try:
        if shell == 'bash':
            if system == 'windows':
                # Windows下的Git Bash
                config_files = [
                    home / '.bashrc',
                    home / '.bash_profile',
                ]
            else:
                config_files = [
                    home / '.bashrc',
                    home / '.bash_profile',
                ]

            for config_file in config_files:
                if config_file.exists():
                    _append_to_config(config_file, f'eval "$(_LEDVANCE_COMPLETE=bash_source {prog_name})"', shell)
                    break
            else:
                # 创建.bashrc
                config_file = home / '.bashrc'
                _append_to_config(config_file, f'eval "$(_LEDVANCE_COMPLETE=bash_source {prog_name})"', shell)

        elif shell == 'zsh':
            config_file = home / '.zshrc'
            _append_to_config(config_file, f'eval "$(_LEDVANCE_COMPLETE=zsh_source {prog_name})"', shell)

        elif shell == 'fish':
            config_dir = home / '.config' / 'fish' / 'completions'
            config_dir.mkdir(parents=True, exist_ok=True)
            config_file = config_dir / f'{prog_name}.fish'

            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(script)
            click.echo(f"✅ Fish补全脚本已安装到: {config_file}")

        elif shell == 'powershell':
            if system == 'windows':
                # PowerShell配置文件路径
                ps_profile_path = subprocess.run(
                    ['powershell', '-Command', 'echo $PROFILE'],
                    capture_output=True,
                    text=True
                ).stdout.strip()

                if ps_profile_path:
                    profile_path = Path(ps_profile_path)
                    profile_path.parent.mkdir(parents=True, exist_ok=True)
                    _append_to_config(profile_path, script, shell)
                else:
                    click.echo("无法确定PowerShell配置文件路径", err=True)
            else:
                click.echo("PowerShell补全仅支持Windows系统", err=True)

    except Exception as e:
        click.echo(f"安装补全脚本失败: {e}", err=True)

def _append_to_config(config_file, content, shell):
    """将补全配置添加到shell配置文件"""
    marker_start = f"# {shell.upper()} completion for ledvance - START"
    marker_end = f"# {shell.upper()} completion for ledvance - END"

    config_file = Path(config_file)

    # 读取现有内容
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            existing_content = f.read()
    else:
        existing_content = ""

    # 检查是否已经存在补全配置
    if marker_start in existing_content:
        click.echo(f"⚠️  {shell}补全配置已存在于 {config_file}")
        return

    # 添加补全配置
    completion_block = f"""
{marker_start}
{content}
{marker_end}
"""

    with open(config_file, 'a', encoding='utf-8') as f:
        f.write(completion_block)

    click.echo(f"✅ {shell}补全配置已添加到: {config_file}")
    click.echo(f"💡 请运行 'source {config_file}' 或重新启动终端以启用补全功能")

if __name__ == "__main__":
    try:
        main()
    except CLIError as e:
        logging.error(str(e), exc_info=True)
        sys.exit(e.exit_code)
    except Exception as e:
        logging.critical(f"发生意外错误: {e}", exc_info=True)
        sys.exit(1)
