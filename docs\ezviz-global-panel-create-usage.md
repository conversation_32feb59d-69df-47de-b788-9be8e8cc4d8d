# ezviz-global-panel-create 命令使用指南

## 概述

`ezviz-global-panel-create` 命令是专门用于创建 Ezviz 全局面板的工具。它基于您提供的原始代码进行了适配，并集成到了 Ledvance CLI 框架中。

## 功能特性

- ✅ 检查全局面板 RN 名称是否重复
- ✅ 获取标签ID列表
- ✅ 上传相关文件（海报图片、Android/iOS 包）
- ✅ 创建全局面板
- ✅ 设置面板为非灰度状态（直接发布）
- ✅ 支持 dry-run 模式
- ✅ 完整的错误处理和日志记录
- ✅ 多种输出格式（text/json/yaml）

## 命令语法

```bash
ledvance ezviz-global-panel-create [OPTIONS]
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--env` | 选择 | `Test` | 平台环境：`Prod` (生产) 或 `Test` (测试) |
| `--base` | 文本 | 可选 | 基于面板名称。如果未指定，使用 --rn-name 的值 |
| `--name` | 文本 | **必需** | 面板名称 |
| `--rn-name` | 文本 | **必需** | RN包名 |
| `--version` | 文本 | `V1.0.0` | RN版本 |
| `--app-version` | 文本 | `2.0.0` | 最低支持的App版本 |
| `--desc` | 文本 | 空字符串 | 描述 |
| `--tags` | 文本 | 可选 | 标签列表，用逗号分隔 |
| `--dir` | 路径 | 可选 | 包所在目录。如果未指定，使用配置中的 base_dir |

## 使用示例

### 1. 基本全局面板创建

```bash
# 创建基本全局面板
ledvance ezviz-global-panel-create \
  --name "我的全局面板" \
  --rn-name "my-global-panel" \
  --tags "智能照明,全局控制"
```

### 2. 指定环境和版本

```bash
# 在生产环境创建全局面板
ledvance ezviz-global-panel-create \
  --env Prod \
  --name "生产全局面板" \
  --rn-name "prod-global-panel" \
  --version "V2.0.0" \
  --app-version "2.1.0" \
  --tags "生产,智能家居"
```

### 3. 使用自定义基础名称和目录

```bash
# 使用自定义基础名称和目录
ledvance ezviz-global-panel-create \
  --name "自定义全局面板" \
  --rn-name "custom-global-panel" \
  --base "base-panel-name" \
  --dir "/path/to/panel/files" \
  --desc "这是一个自定义的全局面板" \
  --tags "自定义,全局"
```

### 4. Dry-run 模式（推荐先使用）

```bash
# 使用 dry-run 模式预览操作
ledvance --dry-run ezviz-global-panel-create \
  --name "测试全局面板" \
  --rn-name "test-global-panel" \
  --tags "测试,预览"
```

### 5. JSON 输出格式

```bash
# 使用 JSON 格式输出结果
ledvance --output json ezviz-global-panel-create \
  --name "JSON输出面板" \
  --rn-name "json-output-panel" \
  --tags "JSON,输出"
```

## 文件结构要求

命令需要以下文件结构（基于 `--base` 或 `--rn-name` 参数）：

```
base_dir/
├── poster/
│   └── {base_name}.PNG          # 海报图片
└── cloud/
    ├── {base_name}-android.zip  # Android 包
    ├── {base_name}-android.md5  # Android MD5 文件
    ├── {base_name}-ios.zip      # iOS 包
    └── {base_name}-ios.md5      # iOS MD5 文件
```

## 与原始代码的对应关系

您提供的原始代码功能已完全适配：

| 原始代码功能 | 新命令对应 |
|-------------|-----------|
| `args.env` | `--env` 参数 |
| `args.base` | `--base` 参数 |
| `args.name` | `--name` 参数 |
| `args.rnName` | `--rn-name` 参数 |
| `args.version` | `--version` 参数 |
| `args.appVersion` | `--app-version` 参数 |
| `args.desc` | `--desc` 参数 |
| `args.tags` | `--tags` 参数 |
| `args.dir` | `--dir` 参数 |
| `get_tag_ids()` 函数 | `_get_tag_ids()` 函数 |
| `create_panel()` 函数 | `_create_global_panel()` 函数 |
| `release_panel()` 函数 | 集成在 `_create_global_panel()` 中 |

## 错误处理

命令包含完整的错误处理：

- ✅ 全局面板名称重复时的错误提示
- ✅ 标签不存在时的错误提示
- ✅ 文件不存在时的错误提示
- ✅ API 调用失败时的重试机制
- ✅ 网络错误的友好提示
- ✅ 参数验证和错误提示

## 日志记录

命令提供详细的日志记录：

- 📝 操作进度跟踪
- 📝 API 调用结果
- 📝 文件上传状态
- 📝 错误详情记录
- 📝 成功/失败统计

## 最佳实践

1. **先使用 dry-run 模式**：在实际操作前先预览
   ```bash
   ledvance --dry-run ezviz-global-panel-create --name "测试" --rn-name "test" --tags "tag1"
   ```

2. **检查文件结构**：确保所需文件存在于正确位置

3. **验证标签**：确保指定的标签在平台中存在

4. **监控日志**：关注操作日志以及时发现问题

5. **备份重要数据**：在生产环境操作前做好备份

## 故障排除

### 常见问题

1. **全局面板名称重复**
   ```
   解决方案：检查面板名称是否已存在，使用不同的 --rn-name
   ```

2. **标签不存在**
   ```
   解决方案：检查标签名称是否正确，确认标签在指定环境中存在
   ```

3. **文件不存在**
   ```
   解决方案：检查文件路径，确保海报图片和包文件存在
   ```

4. **API 调用失败**
   ```
   解决方案：检查网络连接，确认环境配置正确
   ```

### 调试技巧

1. **使用详细日志**：
   ```bash
   ledvance --log-level DEBUG ezviz-global-panel-create ...
   ```

2. **检查配置**：
   ```bash
   # 确保配置文件正确
   cat ~/.config/ledvance/config.yaml
   ```

3. **验证环境变量**：
   ```bash
   # 检查环境变量设置
   cat ~/.config/ledvance/.env
   ```

## 相关命令

- `ezviz-panel-create`: 创建普通面板
- `ezviz-panel-check`: 检查面板版本
- `ezviz-panel-upgrade`: 升级面板
- `ezviz-panel-gray`: 面板灰度操作

## 技术支持

如果遇到问题，请：

1. 检查日志输出中的错误信息
2. 使用 dry-run 模式验证参数
3. 确认文件和配置正确
4. 查看相关文档和示例
