# ezviz-global-panel-upgrade 命令迁移指南

## 概述

本文档说明了如何从原始的 Python 脚本迁移到新的 `ezviz-global-panel-upgrade` CLI 命令。

## 原始脚本 vs 新命令对比

### 命令行参数对比

| 原始脚本参数 | 新命令参数 | 说明 |
|-------------|-----------|------|
| `--env` | `--env` | 环境设置，保持一致 |
| `--type` | `--type` | 操作类型，保持一致 |
| `--dir` | `--dir` | 目录设置，保持一致 |
| `--version` | `--version` | 版本设置，保持一致 |
| `--appVersion` | `--app-version` | App版本，改为kebab-case |
| `--users` | `--users` | 用户列表，保持一致 |
| `--modules` | `--modules` | 模块列表，保持一致 |

### 功能对比

| 功能 | 原始脚本 | 新命令 | 改进 |
|------|---------|--------|------|
| 参数解析 | argparse | Click | 更好的用户体验和验证 |
| 配置管理 | 硬编码配置类 | 统一配置系统 | 更灵活的配置管理 |
| 错误处理 | 基本异常处理 | 完整错误处理 | 更好的错误信息和恢复 |
| 日志记录 | print语句 | 结构化日志 | 更好的调试和监控 |
| 输出格式 | 文本输出 | 多格式输出 | 支持JSON/YAML输出 |
| Dry-run | 无 | 支持 | 安全的预览模式 |
| 进度显示 | 无 | 进度条 | 更好的用户体验 |

## 迁移示例

### 原始脚本调用方式

```bash
python ezviz-global-panel-upgrade.py \
  --env Test \
  --type Upgrade \
  --dir "/path/to/packages" \
  --version "V2.0.0" \
  --appVersion "2.0.16" \
  --users "<EMAIL> <EMAIL>" \
  --modules "module1 module2"
```

### 新命令调用方式

```bash
ledvance ezviz-global-panel-upgrade \
  --env Test \
  --type Upgrade \
  --dir "/path/to/packages" \
  --version "V2.0.0" \
  --app-version "2.0.16" \
  --users "<EMAIL>,<EMAIL>" \
  --modules "module1,module2"
```

### 主要差异

1. **命令前缀**: `python script.py` → `ledvance command`
2. **参数格式**: `--appVersion` → `--app-version`
3. **列表分隔符**: 空格分隔 → 逗号分隔
4. **配置**: 硬编码 → 配置文件

## 配置迁移

### 原始脚本配置

```python
# config_secure.py
class TestConfig:
    BASE_DIR = "/path/to/packages"
    # ... 其他配置

GRAY_ACCOUNTS = ["<EMAIL>", "<EMAIL>"]
DEFAULT_GLOBAL_MODULES = ["module1", "module2"]
```

### 新命令配置

```yaml
# config.yaml
base_dir: "/path/to/packages"
gray_accounts:
  - "<EMAIL>"
  - "<EMAIL>"
default_global_modules:
  - "module1"
  - "module2"
```

## API 调用对比

### 原始脚本

```python
import ezvizApi

# 登录
login_session = ezvizApi.login_platform(config)

# 获取面板
panel_list = ezvizApi.list_global_panel_by_name(login_session, config, module)

# 升级面板
res = ezvizApi.upgrade_global_panel(login_session, config, new_panel)
```

### 新命令

```python
from ledvance_cli.core import ezviz_api

# 自动会话管理
panel_list = ezviz_api.list_global_panel_by_name(module, env=env)

# 升级面板
result = ezviz_api.upgrade_global_panel(new_panel, env=env)
```

## 新增功能

### 1. Dry-run 模式

```bash
# 预览操作而不执行
ledvance --dry-run ezviz-global-panel-upgrade \
  --modules "test-module"
```

### 2. 多种输出格式

```bash
# JSON 输出
ledvance --output json ezviz-global-panel-upgrade \
  --modules "test-module"

# YAML 输出
ledvance --output yaml ezviz-global-panel-upgrade \
  --modules "test-module"
```

### 3. 详细日志

```bash
# 调试级别日志
ledvance --log-level debug ezviz-global-panel-upgrade \
  --modules "test-module"
```

### 4. 进度显示

新命令会显示处理进度：
```
正在处理全局面板升级... ████████████████████ 100%
```

## 错误处理改进

### 原始脚本

```python
if not panel_list or len(panel_list) == 0:
    print(f'Panel {module} not found')
    return False
```

### 新命令

```python
if not panel_list:
    logger.error(f"全局面板 {module} 未找到")
    return False
```

- 使用结构化日志记录
- 更详细的错误信息
- 更好的异常处理和恢复

## 兼容性说明

1. **API兼容**: 使用相同的底层API，确保功能一致性
2. **配置兼容**: 支持环境变量覆盖，便于CI/CD集成
3. **输出兼容**: 默认文本输出与原脚本类似

## 迁移建议

1. **逐步迁移**: 先在测试环境使用新命令
2. **配置验证**: 使用 dry-run 模式验证配置
3. **脚本更新**: 更新CI/CD脚本使用新命令
4. **培训团队**: 确保团队了解新的命令语法

## 故障排除

### 常见问题

1. **命令未找到**
   ```bash
   # 确保已安装CLI
   pip install -e .
   ```

2. **配置错误**
   ```bash
   # 检查配置文件
   ledvance --help
   ```

3. **权限问题**
   ```bash
   # 检查环境变量
   echo $EZVIZ_USERNAME_TEST
   ```

### 调试技巧

1. 使用 `--dry-run` 预览操作
2. 使用 `--log-level debug` 获取详细日志
3. 使用 `--verbose` 获取更多信息
