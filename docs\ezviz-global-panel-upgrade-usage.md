# ezviz-global-panel-upgrade 命令使用指南

## 概述

`ezviz-global-panel-upgrade` 命令是专门用于升级 Ezviz 全局面板的工具。它基于原始的 `ezviz-panel-upgrade` 代码进行了适配，并集成到了 Ledvance CLI 框架中，专门处理全局面板的升级流程。

## 功能特性

- ✅ 支持完整的全局面板升级流程
- ✅ 自动获取全局面板信息和版本
- ✅ 升级全局面板状态
- ✅ 上传文件和更新全局面板信息
- ✅ 设置灰度用户
- ✅ 发布全局面板
- ✅ 支持 dry-run 模式
- ✅ 完整的错误处理和日志记录
- ✅ 多种输出格式（text/json/yaml）
- ✅ 批量处理多个模块
- ✅ 自动版本递增

## 命令语法

```bash
ledvance ezviz-global-panel-upgrade [OPTIONS]
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--env` | 选择 | `Test` | 平台环境：`Prod` (生产) 或 `Test` (测试) |
| `--type` | 选择 | `Upgrade` | 操作类型：`Upgrade` (升级), `Gray` (灰度) 或 `Release` (发布) |
| `--dir` | 路径 | 可选 | 包所在目录。如果未指定，使用配置中的 base_dir |
| `--version` | 文本 | 可选 | 面板版本。如果未指定，将自动递增当前版本 |
| `--app-version` | 文本 | 可选 | 最低 app 版本 |
| `--users` | 文本 | 可选 | 灰度账号列表，用逗号分隔。如果未指定，使用配置中的默认账号 |
| `--modules` | 文本 | 可选 | 模块名称列表，用逗号分隔。如果未指定，使用配置中的默认全局模块 |

## 操作类型说明

### Upgrade (升级)
- 完整的升级流程：升级状态 → 上传文件 → 更新信息 → 设置灰度
- 适用于新版本的发布

### Gray (灰度)
- 跳过升级和上传步骤，只执行灰度用户设置
- 适用于已升级面板的灰度发布

### Release (发布)
- 跳过升级、上传和灰度步骤，直接发布面板
- 适用于灰度测试完成后的正式发布

## 使用示例

### 1. 基本全局面板升级（测试环境）

```bash
# 升级默认全局模块
ledvance ezviz-global-panel-upgrade
```

### 2. 指定模块和版本的升级

```bash
# 升级指定模块到特定版本
ledvance ezviz-global-panel-upgrade \
  --modules "cloud-rn-group-module-light-source,cloud-rn-group-module-matter-light" \
  --version "V2.1.0" \
  --app-version "2.0.16"
```

### 3. 生产环境完整升级流程

```bash
# 生产环境升级
ledvance ezviz-global-panel-upgrade \
  --env Prod \
  --type Upgrade \
  --modules "cloud-rn-group-module-light-source" \
  --version "V2.1.0" \
  --users "<EMAIL>,<EMAIL>"
```

### 4. 灰度发布

```bash
# 对已升级的全局面板进行灰度发布
ledvance ezviz-global-panel-upgrade \
  --env Prod \
  --type Gray \
  --modules "cloud-rn-group-module-light-source" \
  --users "<EMAIL>,<EMAIL>"
```

### 5. 正式发布

```bash
# 灰度测试完成后的正式发布
ledvance ezviz-global-panel-upgrade \
  --env Prod \
  --type Release \
  --modules "cloud-rn-group-module-light-source"
```

### 6. Dry-run 模式（推荐先使用）

```bash
# 使用 dry-run 模式预览操作
ledvance --dry-run ezviz-global-panel-upgrade \
  --env Prod \
  --type Upgrade \
  --modules "cloud-rn-group-module-light-source" \
  --version "V2.1.0"
```

## 配置文件设置

在 `config.yaml` 中可以设置默认值：

```yaml
# 默认全局模块列表
default_global_modules:
  - cloud-rn-group-module-light-source
  - cloud-rn-group-module-matter-light

# 默认灰度账号
gray_accounts:
  - <EMAIL>

# 基础目录
base_dir: "D:/OneDrive - LEDVANCE GmbH/rn-package"
```

## 工作流程

### 完整升级流程 (Upgrade)
1. 🔍 获取全局面板当前信息
2. ⬆️ 升级全局面板状态为新版本
3. 📤 上传 Android/iOS 包文件
4. 🔄 更新全局面板信息（版本、配置等）
5. 👥 设置灰度用户
6. ✅ 完成升级，等待灰度测试

### 灰度发布流程 (Gray)
1. 🔍 获取全局面板当前信息
2. 👥 更新灰度用户列表
3. ✅ 完成灰度设置

### 正式发布流程 (Release)
1. 🔍 获取全局面板当前信息
2. 🚀 取消灰度限制，正式发布
3. ✅ 完成发布

## 注意事项

1. **文件准备**: 确保 `cloud/{module}-android.zip` 和 `cloud/{module}-ios.zip` 文件存在
2. **MD5文件**: 确保对应的 `.md5` 文件存在用于校验
3. **权限**: 确保有足够的权限访问目标环境
4. **测试**: 建议先在测试环境验证，再在生产环境操作
5. **Dry-run**: 重要操作前建议使用 `--dry-run` 模式预览

## 错误处理

- 命令会自动重试失败的操作
- 详细的错误日志帮助定位问题
- 支持部分成功的场景，会列出失败的模块
- 自动调用面板检查命令验证结果

## 与普通面板升级的区别

| 特性 | 普通面板升级 | 全局面板升级 |
|------|-------------|-------------|
| API端点 | `/v3/panel/` | `/v3/global/panel/` |
| 默认模块配置 | `default_device_modules` | `default_global_modules` |
| 面板类型 | 设备面板 | 全局面板 |
| 模块映射 | 支持 `device_module_map` | 直接使用模块名 |
