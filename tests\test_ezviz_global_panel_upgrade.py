"""
测试 ezviz-global-panel-upgrade 命令
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from click.testing import Cli<PERSON>unner
from pathlib import Path

from ledvance_cli.commands.ezviz_global_panel_upgrade import ezviz_global_panel_upgrade
from ledvance_cli.cli import AppContext


@pytest.fixture
def runner():
    """创建CLI测试运行器"""
    return CliRunner()


@pytest.fixture
def mock_context():
    """创建模拟的上下文对象"""
    ctx = Mock()
    ctx.obj = AppContext()
    ctx.obj.output_format = "text"
    ctx.obj.dry_run = False
    ctx.exit = Mock()
    return ctx


@pytest.fixture
def mock_panel_data():
    """创建模拟的全局面板数据"""
    return {
        'panelId': 'test_panel_id',
        'panelName': 'Test Global Panel',
        'rnName': 'test-global-module',
        'version': 'V1.0.0',
        'status': 0,  # STATUS_RELEASED
        'appVersion': '2.0.0',
        'picture': 'test_picture_url',
        'rnDesc': 'Test description',
        'config': '{}',
        'androidConfig': 'android_config',
        'iosConfig': 'ios_config',
        'eibConfig': '{"androidMd5": "test_md5", "iosMd5": "test_md5"}',
        'tagList': [{'id': 1}, {'id': 2}]
    }


class TestEzvizGlobalPanelUpgrade:
    """测试全局面板升级命令"""

    def test_help_command(self, runner):
        """测试帮助命令"""
        result = runner.invoke(ezviz_global_panel_upgrade, ['--help'])
        assert result.exit_code == 0
        assert 'Ezviz 全局面板升级命令' in result.output
        assert '--env' in result.output
        assert '--type' in result.output
        assert '--modules' in result.output

    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.app_config.APP_CONFIG')
    def test_dry_run_mode(self, mock_config, runner):
        """测试dry-run模式"""
        mock_config.get.return_value = "."
        
        result = runner.invoke(ezviz_global_panel_upgrade, [
            '--dry-run',
            '--modules', 'test-module'
        ])
        
        assert result.exit_code == 0
        assert '[DRY-RUN]' in result.output
        assert '目标环境: Test' in result.output
        assert '操作类型: Upgrade' in result.output

    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.ezviz_api')
    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.app_config.APP_CONFIG')
    def test_upgrade_operation_success(self, mock_config, mock_api, runner, mock_panel_data):
        """测试成功的升级操作"""
        # 设置模拟配置
        mock_config.get.side_effect = lambda key, default=None: {
            'base_dir': '.',
            'gray_accounts': ['<EMAIL>'],
            'default_global_modules': ['test-module']
        }.get(key, default)

        # 设置模拟API响应
        mock_api.list_global_panel_by_name.return_value = [mock_panel_data]
        mock_api.upgrade_global_panel.return_value = True
        mock_api.upload_file.return_value = 'mock_config'
        mock_api.update_global_panel.return_value = True
        mock_api.update_global_panel_gray_info.return_value = True

        # 模拟文件存在
        with patch('pathlib.Path.exists', return_value=True), \
             patch('ledvance_cli.commands.ezviz_global_panel_upgrade.read_md5', return_value='test_md5'):
            
            result = runner.invoke(ezviz_global_panel_upgrade, [
                '--modules', 'test-module',
                '--version', 'V2.0.0'
            ])

        assert result.exit_code == 0
        assert '全局面板升级操作完成' in result.output

    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.app_config.APP_CONFIG')
    def test_missing_modules_error(self, mock_config, runner):
        """测试缺少模块配置时的错误处理"""
        mock_config.get.side_effect = lambda key, default=None: {
            'base_dir': '.',
            'gray_accounts': [],
            'default_global_modules': []
        }.get(key, default)

        result = runner.invoke(ezviz_global_panel_upgrade, [])
        
        assert result.exit_code == 1
        assert '未指定模块列表' in result.output

    def test_invalid_operation_type(self, runner):
        """测试无效的操作类型"""
        result = runner.invoke(ezviz_global_panel_upgrade, [
            '--type', 'InvalidType',
            '--modules', 'test-module'
        ])
        
        assert result.exit_code == 2  # Click参数验证错误
        assert 'Invalid value' in result.output

    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.ezviz_api')
    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.app_config.APP_CONFIG')
    def test_panel_not_found(self, mock_config, mock_api, runner):
        """测试面板未找到的情况"""
        mock_config.get.side_effect = lambda key, default=None: {
            'base_dir': '.',
            'gray_accounts': ['<EMAIL>'],
            'default_global_modules': ['test-module']
        }.get(key, default)

        # 模拟面板未找到
        mock_api.list_global_panel_by_name.return_value = []

        result = runner.invoke(ezviz_global_panel_upgrade, [
            '--modules', 'nonexistent-module'
        ])

        assert result.exit_code == 0  # 命令完成但有失败的模块
        assert '失败列表' in result.output

    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.ezviz_api')
    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.app_config.APP_CONFIG')
    def test_gray_operation(self, mock_config, mock_api, runner, mock_panel_data):
        """测试灰度操作"""
        mock_config.get.side_effect = lambda key, default=None: {
            'base_dir': '.',
            'gray_accounts': ['<EMAIL>'],
            'default_global_modules': ['test-module']
        }.get(key, default)

        # 设置面板为灰度状态
        mock_panel_data['status'] = 128  # STATUS_GRAYSCALE
        mock_api.list_global_panel_by_name.return_value = [mock_panel_data]
        mock_api.update_global_panel_gray_info.return_value = True

        result = runner.invoke(ezviz_global_panel_upgrade, [
            '--type', 'Gray',
            '--modules', 'test-module',
            '--users', '<EMAIL>,<EMAIL>'
        ])

        assert result.exit_code == 0
        assert '全局面板升级操作完成' in result.output

    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.ezviz_api')
    @patch('ledvance_cli.commands.ezviz_global_panel_upgrade.app_config.APP_CONFIG')
    def test_release_operation(self, mock_config, mock_api, runner, mock_panel_data):
        """测试发布操作"""
        mock_config.get.side_effect = lambda key, default=None: {
            'base_dir': '.',
            'gray_accounts': ['<EMAIL>'],
            'default_global_modules': ['test-module']
        }.get(key, default)

        mock_api.list_global_panel_by_name.return_value = [mock_panel_data]
        mock_api.update_global_panel_gray_info.return_value = True

        result = runner.invoke(ezviz_global_panel_upgrade, [
            '--type', 'Release',
            '--modules', 'test-module'
        ])

        assert result.exit_code == 0
        assert '全局面板升级操作完成' in result.output
